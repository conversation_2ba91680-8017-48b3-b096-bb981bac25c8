package org.changneng.framework.frameworkbusiness.entity;

import java.util.Date;
import java.util.List;

import org.changneng.framework.frameworkbusiness.pdf.AbstractDocumentVo;
import org.changneng.framework.frameworkbusiness.entity.dto.EnvSupervisionItemDTO;


public class LocalCheckPDFBean  extends AbstractDocumentVo   {
    private String id;

    private String taskId;

    private String objectName;

    private String levelCode;

    private String levelName;

    private String address;

    private String legalPerson;

    private String legalPhone;

    private String checkUserIds;

    private String checkUserNames;
    
    private Integer checkUserLength;

    private String lawEnforcIds;

    private String localPerson;

    private String localPersonPhone;
    
    private String participant;//参与人员

    private String localPersonJob;

    private Date checkStartDate;

    private Date checkEndDate;

    private String isIllegalactCode;

    private String isIllegalactName;  

    private String creatUserId;

    private String creatUserName;

    private Date lastUpdateDate;

    private String updateUserId;

    private String updateUserName;

    private String docUrl;

    private String checkSummary;
    
    private String name ;//XX环保局
    
    private String title;
    
    private List<LocalCheckItem> listItem;
    
    private String startYear;
    
    private String startMouth;
    
    private String startDay;
    
    private String startHour;
    
    private String startMinute;
    
    private String endYear;
    
    private String endMouth;
    
    private String endDay;
    
    private String endHour;
    
    private String endMinute;
    
    private String backgroundImg;
    private String informDeptName;
    
    private String informLawIds;
    
    private String recordUserId;//记录人id
    private String recordUserName;//记录人名称
	
    //private String legalManIdCard;
    
    //private String chargeManIdCard;
    
    private String multiple;

    //--↓↓↓↓↓↓↓↓↓↓--- 环境监管一件事相关字段
    /**
     * 表单类型：0=原有检查项，1=环境监管一件事
     */
    private Integer formType;

    /**
     * 环境监管一件事检查项列表
     */
    private List<EnvSupervisionItemDTO> envSupervisionItems;
    //----------↑↑↑↑↑↑-----

	public String getMultiple() {
		return multiple;
	}

	public void setMultiple(String multiple) {
		this.multiple = multiple;
	}

	//--↓↓↓↓↓↓↓↓↓↓--- 环境监管一件事相关字段的getter/setter
	public Integer getFormType() {
		return formType;
	}

	public void setFormType(Integer formType) {
		this.formType = formType;
	}

	public List<EnvSupervisionItemDTO> getEnvSupervisionItems() {
		return envSupervisionItems;
	}

	public void setEnvSupervisionItems(List<EnvSupervisionItemDTO> envSupervisionItems) {
		this.envSupervisionItems = envSupervisionItems;
	}
	//----------↑↑↑↑↑↑-----

	public String getRecordUserId() {
		return recordUserId;
	}

	public void setRecordUserId(String recordUserId) {
		this.recordUserId = recordUserId;
	}

	public String getRecordUserName() {
		return recordUserName;
	}

	public void setRecordUserName(String recordUserName) {
		this.recordUserName = recordUserName;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getParticipant() {
		return participant;
	}

	public void setParticipant(String participant) {
		this.participant = participant;
	}

	public String getInformDeptName() {
		return informDeptName;
	}

	public void setInformDeptName(String informDeptName) {
		this.informDeptName = informDeptName;
	}

	public String getInformLawIds() {
		return informLawIds;
	}

	public void setInformLawIds(String informLawIds) {
		this.informLawIds = informLawIds;
	}

	public Integer getCheckUserLength() {
		return checkUserLength;
	}

	public void setCheckUserLength(Integer checkUserLength) {
		this.checkUserLength = checkUserLength;
	}

	public List<LocalCheckItem> getListItem() {
		return listItem;
	}

	public void setListItem(List<LocalCheckItem> listItem) {
		this.listItem = listItem;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getId()  {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId == null ? null : taskId.trim();
    }

    public String getObjectName() {
        return objectName;
    }

    public void setObjectName(String objectName) {
        this.objectName = objectName == null ? null : objectName.trim();
    }

    public String getLevelCode() {
        return levelCode;
    }

    public void setLevelCode(String levelCode) {
        this.levelCode = levelCode == null ? null : levelCode.trim();
    }

    public String getLevelName() {
        return levelName;
    }

    public void setLevelName(String levelName) {
        this.levelName = levelName == null ? null : levelName.trim();
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address == null ? null : address.trim();
    }

    public String getLegalPerson() {
        return legalPerson;
    }

    public void setLegalPerson(String legalPerson) {
        this.legalPerson = legalPerson == null ? null : legalPerson.trim();
    }

    public String getLegalPhone() {
        return legalPhone;
    }

    public void setLegalPhone(String legalPhone) {
        this.legalPhone = legalPhone == null ? null : legalPhone.trim();
    }

    public String getcheckUserIds() {
        return checkUserIds;
    }

    public void setcheckUserIds(String checkUserIds) {
        this.checkUserIds = checkUserIds == null ? null : checkUserIds.trim();
    }

    public String getcheckUserNames() {
        return checkUserNames;
    }

    public void setcheckUserNames(String checkUserNames) {
        this.checkUserNames = checkUserNames == null ? null : checkUserNames.trim();
    }

    public String getLawEnforcIds() {
        return lawEnforcIds;
    }

    public void setLawEnforcIds(String lawEnforcIds) {
        this.lawEnforcIds = lawEnforcIds == null ? null : lawEnforcIds.trim();
    }

    public String getLocalPerson() {
        return localPerson;
    }

    public void setLocalPerson(String localPerson) {
        this.localPerson = localPerson == null ? null : localPerson.trim();
    }

    public String getLocalPersonPhone() {
        return localPersonPhone;
    }

    public void setLocalPersonPhone(String localPersonPhone) {
        this.localPersonPhone = localPersonPhone == null ? null : localPersonPhone.trim();
    }

    public String getLocalPersonJob() {
        return localPersonJob;
    }

    public void setLocalPersonJob(String localPersonJob) {
        this.localPersonJob = localPersonJob == null ? null : localPersonJob.trim();
    }

    public Date getCheckStartDate() {
        return checkStartDate;
    }

    public void setCheckStartDate(Date checkStartDate) {
        this.checkStartDate = checkStartDate;
    }

    public Date getCheckEndDate() {
        return checkEndDate;
    }

    public void setCheckEndDate(Date checkEndDate) {
        this.checkEndDate = checkEndDate;
    }

    public String getIsIllegalactCode() {
        return isIllegalactCode;
    }

    public void setIsIllegalactCode(String isIllegalactCode) {
        this.isIllegalactCode = isIllegalactCode == null ? null : isIllegalactCode.trim();
    }

    public String getIsIllegalactName() {
        return isIllegalactName;
    }

    public void setIsIllegalactName(String isIllegalactName) {
        this.isIllegalactName = isIllegalactName == null ? null : isIllegalactName.trim();
    }

    public String getCreatUserId() {
        return creatUserId;
    }

    public void setCreatUserId(String creatUserId) {
        this.creatUserId = creatUserId == null ? null : creatUserId.trim();
    }

    public String getCreatUserName() {
        return creatUserName;
    }

    public void setCreatUserName(String creatUserName) {
        this.creatUserName = creatUserName == null ? null : creatUserName.trim();
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getUpdateUserId() {
        return updateUserId;
    }
    
    

    public String getStartYear() {
		return startYear;
	}

	public void setStartYear(String startYear) {
		this.startYear = startYear;
	}

	public String getStartMouth() {
		return startMouth;
	}

	public void setStartMouth(String startMouth) {
		this.startMouth = startMouth;
	}

	public String getStartDay() {
		return startDay;
	}

	public void setStartDay(String startDay) {
		this.startDay = startDay;
	}

	public String getStartHour() {
		return startHour;
	}

	public void setStartHour(String startHour) {
		this.startHour = startHour;
	}

	public String getStartMinute() {
		return startMinute;
	}

	public void setStartMinute(String startMinute) {
		this.startMinute = startMinute;
	}

	public String getEndYear() {
		return endYear;
	}

	public void setEndYear(String endYear) {
		this.endYear = endYear;
	}

	public String getEndMouth() {
		return endMouth;
	}

	public void setEndMouth(String endMouth) {
		this.endMouth = endMouth;
	}

	public String getEndDay() {
		return endDay;
	}

	public void setEndDay(String endDay) {
		this.endDay = endDay;
	}

	public String getEndHour() {
		return endHour;
	}

	public void setEndHour(String endHour) {
		this.endHour = endHour;
	}

	public String getEndMinute() {
		return endMinute;
	}

	public void setEndMinute(String endMinute) {
		this.endMinute = endMinute;
	}

	public void setUpdateUserId(String updateUserId) {
        this.updateUserId = updateUserId == null ? null : updateUserId.trim();
    }

    public String getUpdateUserName() {
        return updateUserName;
    }

    public void setUpdateUserName(String updateUserName) {
        this.updateUserName = updateUserName == null ? null : updateUserName.trim();
    }

    public String getDocUrl() {
        return docUrl;
    }

    public void setDocUrl(String docUrl) {
        this.docUrl = docUrl == null ? null : docUrl.trim();
    }


	public String getCheckSummary() {
		return checkSummary;
	}

	public void setCheckSummary(String checkSummary) {
		this.checkSummary = checkSummary;
	}
	

	public String getBackgroundImg() {
		return backgroundImg;
	}

	public void setBackgroundImg(String backgroundImg) {
		this.backgroundImg = backgroundImg;
	}

	@Override
	public String findPrimaryKey() {
		// TODO Auto-generated method stub
		return null;
	}
/*
	public String getLegalManIdCard() {
		return legalManIdCard;
	}

	public void setLegalManIdCard(String legalManIdCard) {
		this.legalManIdCard = legalManIdCard;
	}

	public String getChargeManIdCard() {
		return chargeManIdCard;
	}

	public void setChargeManIdCard(String chargeManIdCard) {
		this.chargeManIdCard = chargeManIdCard;
	}*/
}
