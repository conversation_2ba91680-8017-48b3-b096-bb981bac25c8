package org.changneng.framework.frameworkbusiness.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.changneng.framework.frameworkbusiness.entity.LocalCheckItem;
//--↓↓↓↓↓↓↓↓↓↓---
import org.changneng.framework.frameworkbusiness.entity.dto.EnvSupervisionItemDTO;
//----------↑↑↑↑↑↑-----

public interface LocalCheckItemMapper {
    int deleteByPrimaryKey(String id);

    int insert(LocalCheckItem record);

    int insertSelective(LocalCheckItem record);

    LocalCheckItem selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(LocalCheckItem record);

    int updateByPrimaryKeyWithBLOBs(LocalCheckItem record);

    int updateByPrimaryKey(LocalCheckItem record);

    List<LocalCheckItem>  getChickItemItem(@Param("taskId")String taskId, @Param("localChickId")String localChickId);

	List<LocalCheckItem> getChickItemItemByTaskId(@Param("taskId")String taskId);

	void updateByLocalCheckItemId(@Param("localCheckitemId")String localCheckitemId,@Param("status") String status);

	void updateRemarkCheckItemById(@Param("localCheckitemId")String localCheckitemId,
			@Param("remarkCheckItemText")String remarkCheckItemText);

	void updateChickItemByTaskId(@Param("localCheckId")String localCheckId, @Param("taskId")String taskId);

	List<LocalCheckItem> getChickItemItemList(@Param("taskId")String taskId);

	List<LocalCheckItem> getSpecialItemItemList(@Param("taskId")String taskId);
	
	void saveLocalChickList(@Param("chickItemItem")List<LocalCheckItem> chickItemItem);
	
	/**
	 * 根据专项行动code不同查询关联项
	 * @param TemplateType
	 * @return
	 */
	List<LocalCheckItem> getByTemplateType(@Param("TemplateType")String TemplateType);

	List<LocalCheckItem> getBehIdByCaseId(@Param("caseId")String caseId);
	/**
	 * 删除检查表检查项 根据检查表id
	 * @param id
	 */
	void deleteByLocalCheckId(@Param("localCheckId")String localCheckId);

	List<LocalCheckItem> getInfoByid(@Param("id")String id);

	//--↓↓↓↓↓↓↓↓↓↓---
	/**
	 * 查询环境监管一件事检查项
	 * @param localCheckId 本地检查ID
	 * @return 检查项列表
	 */
	List<LocalCheckItem> selectEnvSupervisionItems(String localCheckId);

	/**
	 * 根据本地检查ID和表单类型删除检查项
	 * @param localCheckId 本地检查ID
	 * @param formType 表单类型：0=原有检查项，1=环境监管一件事
	 * @return 删除的记录数
	 */
	int deleteByLocalCheckIdAndFormType(@Param("localCheckId") String localCheckId, @Param("formType") Integer formType);

	/**
	 * 批量插入环境监管一件事检查项
	 * @param items 检查项列表
	 * @return 插入的记录数
	 */
	int batchInsertEnvSupervisionItems(@Param("items") List<EnvSupervisionItemDTO> items);

	/**
	 * 批量插入检查项（支持UPSERT策略）
	 * @param items 检查项列表
	 * @return 插入的记录数
	 */
	int batchInsertLocalCheckItems(@Param("items") List<LocalCheckItem> items);

	/**
	 * 批量更新检查项（支持UPSERT策略）
	 * @param items 检查项列表
	 * @return 更新的记录数
	 */
	int batchUpdateLocalCheckItems(@Param("items") List<LocalCheckItem> items);

	/**
	 * 根据ID列表批量删除检查项（支持UPSERT策略）
	 * @param ids ID列表
	 * @return 删除的记录数
	 */
	int batchDeleteByIds(@Param("ids") List<String> ids);
	//----------↑↑↑↑↑↑-----
}
