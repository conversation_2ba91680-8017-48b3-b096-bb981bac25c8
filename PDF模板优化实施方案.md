# 现场检查表PDF模板优化实施方案

## 📋 项目概述

本方案实现了现场检查表PDF导出模板的优化，使其能够支持新增的"环境监管一件事"表单功能，同时保持对原有检查项表单的完全兼容。

## 🔧 实施内容

### 1. 数据模型扩展

#### 1.1 LocalCheckPDFBean.java 修改
**文件位置：** `framework-business/src/main/java/org/changneng/framework/frameworkbusiness/entity/LocalCheckPDFBean.java`

**新增字段：**
```java
/**
 * 表单类型：0=原有检查项，1=环境监管一件事
 */
private Integer formType;

/**
 * 环境监管一件事检查项列表
 */
private List<EnvSupervisionItemDTO> envSupervisionItems;
```

**新增导入：**
```java
import org.changneng.framework.frameworkbusiness.entity.dto.EnvSupervisionItemDTO;
```

### 2. 服务层逻辑优化

#### 2.1 LocalExamineServiceImpl.java 修改
**文件位置：** `framework-business/src/main/java/org/changneng/framework/frameworkbusiness/service/impl/LocalExamineServiceImpl.java`

**修改位置1：** saveLocalExamine方法中的PDF Bean构建逻辑（约第248行）
**修改位置2：** appSaveLocalExamine方法中的PDF Bean构建逻辑（约第718行）

**新增逻辑：**
```java
//--↓↓↓↓↓↓↓↓↓↓--- 设置表单类型和环境监管一件事数据
bean.setFormType(localCheck.getFormType());

// 如果是环境监管一件事表单，加载相关数据
if (Integer.valueOf(1).equals(localCheck.getFormType()) && !ChangnengUtil.isNull(localCheck.getId())) {
    try {
        List<LocalCheckItem> envItems = loadEnvSupervisionItems(localCheck.getId());
        if (envItems != null && !envItems.isEmpty()) {
            // 转换为EnvSupervisionItemDTO列表
            List<EnvSupervisionItemDTO> envSupervisionItems = new ArrayList<>();
            for (LocalCheckItem item : envItems) {
                EnvSupervisionItemDTO dto = new EnvSupervisionItemDTO();
                dto.setConfigItemId(item.getConfigItemId());
                dto.setItemName(item.getCheckItemName());
                dto.setResult(item.getCheckItemResult());
                dto.setProblemDesc(item.getProblemDesc());
                envSupervisionItems.add(dto);
            }
            bean.setEnvSupervisionItems(envSupervisionItems);
        }
    } catch (Exception e) {
        logger.warn("加载环境监管一件事数据失败，localCheckId: {}", localCheck.getId(), e);
    }
}
//----------↑↑↑↑↑↑-----
```

### 3. PDF模板优化

#### 3.1 localExamineModel.html 修改
**文件位置：** `framework-web/src/main/resources/pdfConfig/templet/localExamineModel.html`

**修改位置：** 第265-276行的检查内容显示部分

**新增条件判断逻辑：**
```html
<!--↓↓↓↓↓↓↓↓↓↓ 根据表单类型显示不同内容 -->
<#if formType?? && formType == 1>
    <!-- 环境监管一件事表单内容 -->
    <#if envSupervisionItems?? && (envSupervisionItems?size > 0)>
        <table style="width: 100%; border-collapse: collapse; margin-top: 10px;">
            <thead>
                <tr style="background-color: #f5f5f5;">
                    <th style="border: 1px solid #444444; padding: 8px; text-align: center; font-weight: bold; width: 50%;">检查项名称</th>
                    <th style="border: 1px solid #444444; padding: 8px; text-align: center; font-weight: bold; width: 15%;">检查结果</th>
                    <th style="border: 1px solid #444444; padding: 8px; text-align: center; font-weight: bold; width: 35%;">问题简述</th>
                </tr>
            </thead>
            <tbody>
                <#list envSupervisionItems as item>
                    <tr>
                        <td style="border: 1px solid #444444; padding: 8px; text-align: left; vertical-align: top;">
                            <#if item.itemName??>${item.itemName}<#else>&#160;</#if>
                        </td>
                        <td style="border: 1px solid #444444; padding: 8px; text-align: center; vertical-align: top;">
                            <#if item.result??>
                                <#if item.result == "1">是
                                <#elseif item.result == "0">否
                                <#elseif item.result == "2">不涉及
                                <#else>${item.result}
                                </#if>
                            <#else>&#160;</#if>
                        </td>
                        <td style="border: 1px solid #444444; padding: 8px; text-align: left; vertical-align: top; word-wrap: break-word;">
                            <#if item.problemDesc?? && item.problemDesc != "">${item.problemDesc}<#else>&#160;</#if>
                        </td>
                    </tr>
                </#list>
            </tbody>
        </table>
    <#else>
        <div style="text-align: center; padding: 20px; color: #666;">
            暂无环境监管一件事检查项数据
        </div>
    </#if>
<#else>
    <!-- 原有检查项表单内容 -->
    <#if listItem??>
         <#list listItem as item> 
        <span  style="border: 0; font-size: 15px;  text-align: left; ">
        <#if item.checkItemName??><span   class="span11x">${item.checkItemName}</span></#if>
        <#if item.checkItemResult??><span   class="span33x"> ${item.checkItemResult}</span></#if>
        <span  class="span1"> 备注：<#if item.remark??><u class="span33x">${item.remark}&#160;</u><#else><u class="span3">&#160;&#160;&#160;&#160;&#160;&#160;&#160;</u></#if></span>
        </span>
        <br/>
       </#list> 
    </#if>
</#if>
<!--↑↑↑↑↑↑↑↑↑↑ 根据表单类型显示不同内容 -->
```

## 🔍 核心特性

### 1. 条件判断逻辑
- **formType = 1**：使用环境监管一件事表单模板，显示表格化的检查项数据
- **formType = 0 或 null**：使用原有检查项表单模板，保持现有显示格式

### 2. 数据映射
- **检查结果映射**：
  - `1` → "是"
  - `0` → "否"  
  - `2` → "不涉及"
- **问题简述**：支持长文本显示，自动换行

### 3. 表格样式
- **响应式布局**：检查项名称50%，检查结果15%，问题简述35%
- **边框样式**：统一的表格边框和内边距
- **文本对齐**：检查项名称和问题简述左对齐，检查结果居中对齐

## 🧪 测试验证

### 1. 测试文件
**文件位置：** `framework-web/src/test/java/PdfTemplateTest.java`

### 2. 测试用例
1. **testOriginalCheckItemPdf()**：测试原有检查项PDF生成
2. **testEnvSupervisionPdf()**：测试环境监管一件事PDF生成
3. **testPdfTemplateCompatibility()**：测试模板兼容性

### 3. 运行测试
```bash
# 在项目根目录执行
mvn test -Dtest=PdfTemplateTest
```

## 📊 兼容性保证

### 1. 向后兼容
- **现有功能不受影响**：原有检查项表单的PDF生成逻辑完全保持不变
- **默认行为**：当formType为null或0时，自动使用原有模板

### 2. 数据安全
- **空值处理**：所有字段都有空值检查和默认显示
- **异常处理**：环境监管一件事数据加载失败时不影响PDF生成

### 3. 性能优化
- **条件加载**：只有在formType=1时才加载环境监管一件事数据
- **缓存机制**：利用现有的PDF生成缓存机制

## 🚀 部署说明

### 1. 部署步骤
1. **备份现有文件**：备份LocalCheckPDFBean.java、LocalExamineServiceImpl.java、localExamineModel.html
2. **应用修改**：按照上述修改内容更新文件
3. **编译项目**：重新编译整个项目
4. **重启服务**：重启应用服务器
5. **功能测试**：验证两种表单类型的PDF生成功能

### 2. 回滚方案
如果出现问题，可以快速回滚到备份的文件版本，确保系统稳定性。

## 📝 使用示例

### 1. 原有检查项表单
```java
LocalCheck localCheck = new LocalCheck();
localCheck.setFormType(0); // 或者不设置，默认为原有检查项
// ... 其他字段设置
// PDF生成时会使用原有的检查项显示格式
```

### 2. 环境监管一件事表单
```java
LocalCheck localCheck = new LocalCheck();
localCheck.setFormType(1); // 环境监管一件事
// ... 其他字段设置
// PDF生成时会使用表格化的环境监管一件事显示格式
```

## 🔧 技术架构

### 1. 技术栈
- **模板引擎**：FreeMarker
- **PDF生成**：iText + Flying Saucer
- **数据传输**：EnvSupervisionItemDTO
- **文件存储**：FastDFS

### 2. 数据流
1. **数据收集**：前端表单 → Controller → Service
2. **数据转换**：LocalCheckItem → EnvSupervisionItemDTO
3. **模板渲染**：FreeMarker处理HTML模板
4. **PDF生成**：iText将HTML转换为PDF
5. **文件存储**：FastDFS存储PDF文件

## 📞 技术支持

如有问题，请检查：
1. **数据库表结构**：确保LOCAL_CHECK表有FORM_TYPE字段
2. **模板文件**：确保localExamineModel.html修改正确
3. **服务方法**：确保loadEnvSupervisionItems方法正常工作
4. **日志信息**：查看应用日志中的错误信息

---

**实施完成后，系统将支持两种PDF模板的无缝切换，为用户提供更好的使用体验。**
