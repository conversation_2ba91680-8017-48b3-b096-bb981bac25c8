import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import org.changneng.framework.frameworkbusiness.service.LocalExamineService;
import org.changneng.framework.frameworkbusiness.entity.LocalCheck;
import org.changneng.framework.frameworkbusiness.entity.LocalCheckPDFBean;
import org.changneng.framework.frameworkbusiness.entity.dto.EnvSupervisionItemDTO;
import org.changneng.framework.frameworkbusiness.pdf.GeneratePdfService;
import org.changneng.framework.frameworkcore.utils.ResourceLoader;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * PDF模板测试类
 * 用于验证环境监管一件事PDF生成功能
 * 
 * <AUTHOR> Generated
 * @date 2025-01-31
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring-context.xml"})
public class PdfTemplateTest {

    @Autowired
    private GeneratePdfService generatePdfService;

    /**
     * 测试原有检查项PDF生成
     */
    @Test
    public void testOriginalCheckItemPdf() {
        try {
            System.out.println("=== 测试原有检查项PDF生成 ===");
            
            // 创建测试数据
            LocalCheckPDFBean bean = createBasicPdfBean();
            bean.setFormType(0); // 原有检查项
            
            // 设置原有检查项数据
            // 这里可以添加原有检查项的测试数据
            
            // 生成PDF
            String templatePath = File.separator + "pdfConfig" + File.separator + "templet" + File.separator + "localExamineModel.html";
            String pdfPath = generatePdfService.generateLocalCheckPdf(bean, templatePath);
            
            if (pdfPath != null && !pdfPath.isEmpty()) {
                System.out.println("✅ 原有检查项PDF生成成功: " + pdfPath);
                
                // 验证文件是否存在
                File pdfFile = new File(pdfPath);
                if (pdfFile.exists()) {
                    System.out.println("✅ PDF文件存在，大小: " + pdfFile.length() + " bytes");
                } else {
                    System.err.println("❌ PDF文件不存在");
                }
            } else {
                System.err.println("❌ 原有检查项PDF生成失败");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 原有检查项PDF生成测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试环境监管一件事PDF生成
     */
    @Test
    public void testEnvSupervisionPdf() {
        try {
            System.out.println("=== 测试环境监管一件事PDF生成 ===");
            
            // 创建测试数据
            LocalCheckPDFBean bean = createBasicPdfBean();
            bean.setFormType(1); // 环境监管一件事
            
            // 设置环境监管一件事数据
            List<EnvSupervisionItemDTO> envItems = createTestEnvSupervisionItems();
            bean.setEnvSupervisionItems(envItems);
            
            // 生成PDF
            String templatePath = File.separator + "pdfConfig" + File.separator + "templet" + File.separator + "localExamineModel.html";
            String pdfPath = generatePdfService.generateLocalCheckPdf(bean, templatePath);
            
            if (pdfPath != null && !pdfPath.isEmpty()) {
                System.out.println("✅ 环境监管一件事PDF生成成功: " + pdfPath);
                
                // 验证文件是否存在
                File pdfFile = new File(pdfPath);
                if (pdfFile.exists()) {
                    System.out.println("✅ PDF文件存在，大小: " + pdfFile.length() + " bytes");
                } else {
                    System.err.println("❌ PDF文件不存在");
                }
            } else {
                System.err.println("❌ 环境监管一件事PDF生成失败");
            }
            
        } catch (Exception e) {
            System.err.println("❌ 环境监管一件事PDF生成测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 测试PDF模板兼容性
     */
    @Test
    public void testPdfTemplateCompatibility() {
        try {
            System.out.println("=== 测试PDF模板兼容性 ===");
            
            // 测试formType为null的情况（应该使用原有检查项模板）
            LocalCheckPDFBean bean1 = createBasicPdfBean();
            bean1.setFormType(null);
            
            String templatePath = File.separator + "pdfConfig" + File.separator + "templet" + File.separator + "localExamineModel.html";
            String pdfPath1 = generatePdfService.generateLocalCheckPdf(bean1, templatePath);
            
            if (pdfPath1 != null) {
                System.out.println("✅ formType为null时PDF生成成功（兼容性测试通过）");
            } else {
                System.err.println("❌ formType为null时PDF生成失败");
            }
            
            // 测试formType为0的情况
            LocalCheckPDFBean bean2 = createBasicPdfBean();
            bean2.setFormType(0);
            
            String pdfPath2 = generatePdfService.generateLocalCheckPdf(bean2, templatePath);
            
            if (pdfPath2 != null) {
                System.out.println("✅ formType为0时PDF生成成功");
            } else {
                System.err.println("❌ formType为0时PDF生成失败");
            }
            
        } catch (Exception e) {
            System.err.println("❌ PDF模板兼容性测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 创建基础PDF Bean
     */
    private LocalCheckPDFBean createBasicPdfBean() {
        LocalCheckPDFBean bean = new LocalCheckPDFBean();
        
        // 设置基本信息
        bean.setId("test-id-" + System.currentTimeMillis());
        bean.setTaskId("test-task-id");
        bean.setObjectName("测试企业有限公司");
        bean.setAddress("测试省测试市测试区测试街道123号");
        bean.setLegalPerson("张三");
        bean.setLegalPhone("13800138000");
        bean.setcheckUserNames("李四,王五");
        bean.setLawEnforcIds("12345,67890");
        bean.setLocalPerson("赵六");
        bean.setLocalPersonPhone("13900139000");
        bean.setLocalPersonJob("环保负责人");
        bean.setRecordUserName("记录员");
        bean.setParticipant("参与人员1,参与人员2");
        
        // 设置时间信息
        Date now = new Date();
        bean.setCheckStartDate(now);
        bean.setCheckEndDate(now);
        bean.setStartYear("2025");
        bean.setStartMouth("01");
        bean.setStartDay("31");
        bean.setStartHour("09");
        bean.setStartMinute("00");
        bean.setEndYear("2025");
        bean.setEndMouth("01");
        bean.setEndDay("31");
        bean.setEndHour("17");
        bean.setEndMinute("00");
        
        // 设置单位信息
        bean.setName("测试环保局");
        bean.setTitle("现场检查表");
        bean.setInformDeptName("测试环保局");
        bean.setInformLawIds("12345,67890");
        
        // 设置背景图片
        try {
            bean.setBackgroundImg(ResourceLoader.getPdfBackgroundImg("pdf_locakExamine.jpg"));
        } catch (Exception e) {
            System.warn("设置背景图片失败: " + e.getMessage());
        }
        
        return bean;
    }

    /**
     * 创建测试用的环境监管一件事数据
     */
    private List<EnvSupervisionItemDTO> createTestEnvSupervisionItems() {
        List<EnvSupervisionItemDTO> items = new ArrayList<>();
        
        // 添加测试数据
        EnvSupervisionItemDTO item1 = new EnvSupervisionItemDTO();
        item1.setConfigItemId("0_0");
        item1.setItemName("是否建立环境保护管理制度");
        item1.setResult("1"); // 是
        item1.setProblemDesc("已建立完善的环境保护管理制度");
        items.add(item1);
        
        EnvSupervisionItemDTO item2 = new EnvSupervisionItemDTO();
        item2.setConfigItemId("0_1");
        item2.setItemName("是否设置环保管理机构或专职人员");
        item2.setResult("0"); // 否
        item2.setProblemDesc("未设置专职环保管理人员，建议尽快配备");
        items.add(item2);
        
        EnvSupervisionItemDTO item3 = new EnvSupervisionItemDTO();
        item3.setConfigItemId("1_0");
        item3.setItemName("是否存在超标排放情况");
        item3.setResult("2"); // 不涉及
        item3.setProblemDesc("");
        items.add(item3);
        
        EnvSupervisionItemDTO item4 = new EnvSupervisionItemDTO();
        item4.setConfigItemId("1_1");
        item4.setItemName("污染防治设施是否正常运行");
        item4.setResult("1"); // 是
        item4.setProblemDesc("污染防治设施运行正常，各项指标符合要求");
        items.add(item4);
        
        return items;
    }
}
