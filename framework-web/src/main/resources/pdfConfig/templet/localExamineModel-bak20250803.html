<html>
<head>
<meta http-equiv="Content-type" content="text/html; charset=utf-8"></meta>
<title></title>
<style type="text/css">
body {
	margin-left: 45px;
	margin-right: 45px;
	font-family: SimSun;
	
	font-size: 10px;
}

table {
	margin: auto;
	width: 100%;
	border-collapse: collapse;
	border: 1px solid #444444;
}

th,td {
	border: 1px solid #444444;
	font-size: 10px;
	margin-left: 5px;
}
u {
    text-decoration: none;
    border-bottom: 1px solid black;
  }

.mcContent {
	line-height: 180%;
	padding: 20px;
}

.logo {
	text-align: center;
}

.title {
	text-align: center;
	font-weight: bold;
	font-size: 20px;
}

.notes {
	font-weight: normal;
	margin-left: 5px;
	margin-right: 5px;
	line-height: 18px;
	font-size: 16px;
}

.text_content {
	margin-left: 5px;
	margin-right: 5px;
	line-height: 18px;
}

.sum_insured_first_row {
	width: 20%;
}

.sum_insured_span {
	font-size: 10px;
}

.special_agreements_div {
	page-break-before: always;
	font-size: 14px;
	margin-top: 20px;
}

.special_agreements_div .special_agreements {
	font-size: 18px;
	font-weight: bold;
}

.title_right {
	width: 100%;
	margin: 0 auto;
}

.title_right p {
	text-align: left;
	margin: 0;
	margin-left: 50%;
	padding: 0;
}
/* 页码   */
@page {   
    size: 8.5in 11in;
    margin: 0.25in;   
    padding: 1em;  
    background:url('${backgroundImg!}');
    margin-bottom:170px;
    padding: 1em;  
    @bottom-center{content:""; };  
    @bottom-right{  
            content:"第" counter(page) "页  共 " counter(pages) "页";  
            font-family: SimSun;   
        font-size: 14px;  
        color:#000;   
        padding:10px;
        vertical-align: bottom; 
     }; 
 }  
             
div#myheader {  
    display: block;  
    position: running(header);  
}   
  

#pagenumber:before {  
content: counter(page);   
}  
  
#pagecount:before {  
content: counter(pages);    
}  
  /* 页码   */
.signature {
	margin: 0 auto;
	clear: both;
	font-size: 16px;
	font-weight: bold;
}

.signature_table {
/* 	font-size: 16px; */
	font-weight: bold;

}

.content{
	display: inline-block;
	width: 521px;
	border-bottom: 1px solid black;
}
.content1{
	display: inline-block;
	width: 541px;
	border-bottom: 1px solid black;
}
.content2{
	display: inline-block;
	width: 526px;
	border-bottom: 1px solid black;
}
.content_half{
	display: inline-block;
	width: 236px;
	border-bottom: 1px solid black;
}
.content_three{
	display: inline-block;
	border-bottom: 1px solid black;
}
.content_four{
	width: 31px;
	border-bottom: 1px solid black;
	line-height: 25px;/*设置行高*/
}
.content_four1{
	width: 31px;
	border-bottom: 1px solid black;
	line-height: 25px;/*设置行高*/
}

.content_five{
	border-bottom: 1px solid black;
	text-align: center;
}

.content_hr{
	font-weight: bold;
	font-size: 15px;
	line-height: 25px;
}
.span1{
	line-height: 25px;
}
.span2{
	line-height: 30px;/*设置行高*/
}
.span22x{
	<#if multiple?? && multiple!="">line-height: ${multiple}px;/*设置行高*/<#else>line-height: 35px;</#if>
}
.span22{
	line-height: 30px;/*设置行高*/
}
.span222{
	line-height: 40px;/*设置行高*/
}
.span3{
	line-height: 25px;
	border-bottom: 1px solid black;
}
.span11{
	line-height: 25px;
}
.span33{
	line-height: 25px;
	border-bottom: 1px solid black;
}
.span11x{
	<#if multiple?? && multiple!="">line-height: ${multiple}px;/*设置行高*/<#else>line-height: 30px;</#if>
}
.span33x{
	<#if multiple?? && multiple!="">line-height: ${multiple}px;<#else>line-height: 30px;</#if>
	border-bottom: 1px solid black;
}
.span111{
	line-height: 45px;
}
.span333{
	line-height: 45px;
	border-bottom: 1px solid black;
}
/**/
</style>
</head>
<body>
		<div class="title">
			<#if name??>${name}</#if><br /> 
			<#if title??>${title}</#if><br />
			<hr/>
		</div>
		<div style="border: 0; font-size: 15px; margin-left:10px; text-align: left; "  >
			被检查单位：<span class ="content span22x" style="display: inline-block;width: 514px;" > <#if objectName??>${objectName}<#else>&#160;</#if></span><br/>
			详细地址：<span  class ="span22x" style="display: inline-block;width: 526px;border-bottom: 1px solid black;"> <#if address??> ${address}<#else>&#160;</#if></span><br/>
			法定代表人：<span class ="span22x" style="display: inline-block;width: 213px;border-bottom: 1px solid black;"><#if legalPerson??>${legalPerson}&#160;<#else>&#160;</#if></span>
			法人电话：<span  class ="span22x" style="display: inline-block;width: 217px;border-bottom: 1px solid black;" ><#if legalPhone??>${legalPhone}&#160;<#else>&#160;</#if></span><br/>
			检 查 人：<span  class ="content1 span22x" style="width:526px"><#if checkUserNames??>${checkUserNames}<#else>&#160;</#if></span><br/>
			执法证号：<span class="span22x" style="display: inline-block;width: 526px;border-bottom: 1px solid black;word-break:normal;  white-space:pre-wrap;word-wrap : break-word ;overflow: hidden ;" ><#if lawEnforcIds??>${lawEnforcIds}<#else>&#160;</#if></span><br/>
			记   录   人：<span  class="span22x" style="display: inline-block;width: 526px;border-bottom: 1px solid black;word-break:normal;  white-space:pre-wrap;word-wrap : break-word ;overflow: hidden ;"><#if recordUserName??>${recordUserName}&#160;<#else>&#160;</#if></span><br/>
			参与人员及其工作单位：<span style="line-height: 30px;"></span><br/>
			<span style="line-height: 25px;"><#if participant??> 
			<#if participant!="">
			<div class="col-md-6" style="word-wrap:break-word;word-break:break-all;">&#160;&#160;<u style="line-height: 25px;border-bottom: 1px solid black;">${participant}&#160;</u></div>
			<#else>
			<div style="display: inline-block;width: 600px;border-bottom: 1px solid black;word-break:normal;  white-space:pre-wrap;word-wrap : break-word ;overflow: hidden ;"></div><br/>
			</#if>
			<#else><div style="display: inline-block;width: 600px;border-bottom: 1px solid black;word-break:normal;  white-space:pre-wrap;word-wrap : break-word ;overflow: hidden ;"></div><br/></#if></span>
			被检查单位现场负责人：<span class ="content_three span22x" ><#if localPerson??>${localPerson}&#160;<#else>&#160;</#if></span>
			 职  务：<span class ="content_three span22x"  style ="display: inline-block;width: 104px;border-bottom: 1px solid black;"><#if localPersonJob??>${localPersonJob}&#160;<#else>&#160;</#if></span>
			 联系电话：<span  class ="content_three span22" style ="display: inline-block;width: 130px;border-bottom: 1px solid black;"><#if localPersonPhone??>${localPersonPhone}&#160;<#else>&#160;</#if></span><br/>
			 检查时间：<span class ="content_four"><#if startYear??>${startYear}<#else>&#160;</#if> </span>年   
			<span class ="content_four1"><#if startMouth??>${startMouth}<#else>&#160;</#if></span> 月
			  <span class ="content_four1"> <#if startDay??>${startDay}<#else>&#160;</#if> </span>日 
			  <span class ="content_four1"> <#if startHour??>${startHour}<#else>&#160;</#if></span> 时   
			  <span class ="content_four1"><#if startMinute??>${startMinute}<#else>&#160;</#if>  </span>分至      
			  <span class ="content_four"> <#if endYear??>${endYear}<#else>&#160;</#if> </span>年   
			  <span class ="content_four1"> <#if endMouth??>${endMouth}<#else>&#160;</#if> </span>月    
			  <span class ="content_four1"> <#if endDay??>${endDay}<#else>&#160;</#if> </span>日    
			  <span class ="content_four1"> <#if endHour??>${endHour}<#else>&#160;</#if> </span>时     
			  <span class ="content_four1"> <#if endMinute??>${endMinute}<#else>&#160;</#if> </span>分<br/>
			  <hr class="title"/>
			 <div style="line-height: 26px" class="span22x"> <div  style="font-weight:bold;">告知事项：我们是<span class="content_five" <#if informDeptName?? && informDeptName!="">style="width:auto"<#else>style="width:45%;display:inline-block;"</#if>><#if informDeptName??>&#160;${informDeptName}&#160;<#else>&#160;</#if></span>的行政执法人员，这是我们的执法证件
			（执法证编号：<span class="content_five span22x" <#if informLawIds?? && informLawIds!="">style="width:auto"<#else>style="width:65%;display:inline-block;"</#if>> <#if informLawIds?? && informLawIds!='' >${informLawIds}&#160;<#else>&#160;</#if></span>）。	
			<br/>请过目确认：<span style="display: inline-block;width: 530px;border-bottom: 1px solid black;">&#160; </span> 
			<br/><span class="span22x">今天我们依法进行检查并了解有关情况，你应当配合调查，如实提供材料，不得拒绝、阻碍、隐瞒或者提供虚假情况。如果你认为检查人与本案有利害关系，
			可能影响公正办案，可以申请回避，并说明理由。请确认：<span style="display: inline-block;width: 420px;border-bottom: 1px solid black;">&#160; </span></span></div></div>
		<hr class="title"/>
		<div class ='content_hr'>一、现场检查内容:</div>
		<#if listItem??>
			 <#list listItem as item> 
            <span  style="border: 0; font-size: 15px;  text-align: left; ">
            <#if item.checkItemName??><span   class="span11x">${item.checkItemName}</span></#if>
            <#if item.checkItemResult??><span   class="span33x"> ${item.checkItemResult}</span></#if>
            <span  class="span1"> 备注：<#if item.remark??><u class="span33x">${item.remark}&#160;</u><#else><u class="span3">&#160;&#160;&#160;&#160;&#160;&#160;&#160;</u></#if></span>
            </span>
            <br/>
           </#list> 
		</#if>
		<!-- <div class ='content_hr'>二、监察小结：<div style="float:right;font-weight:normal;">是否存在违法行为？ <span class="span3"><#if isIllegalactName??>${isIllegalactName}<#else>&#160;</#if></span></div></div>
		<span class="span1"><#if checkSummary??> <div class="col-md-6" style="word-wrap:break-word;word-break:break-all;">&#160;&#160;<u class="span3">${checkSummary}</u></div><#else>&#160;</u></div></#if></span><br /> -->
		</div>
		<br />
	<div style="font-size: 16px;margin-top: -5px;margin-bottom: -5px;"><span>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;（以下空白）</span></div>
	
</body>
</html>